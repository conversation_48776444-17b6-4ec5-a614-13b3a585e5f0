"""
Manticore API 数据模型
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime

class Document(BaseModel):
    """文档模型"""
    id: int = Field(..., description="文档ID")
    title: str = Field(..., description="文档标题")
    content: str = Field(..., description="文档内容")
    category: str = Field(..., description="文档分类")
    created_at: Optional[int] = Field(None, description="创建时间戳")
    
    @validator('title', 'content')
    def validate_text_fields(cls, v):
        if not v or not v.strip():
            raise ValueError("标题和内容不能为空")
        return v.strip()

class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: str = Field(..., description="搜索关键词")
    limit: int = Field(10, ge=1, le=100, description="返回结果数量")
    offset: int = Field(0, ge=0, description="结果偏移量")
    category: Optional[str] = Field(None, description="分类过滤")
    highlight: bool = Field(True, description="是否高亮显示")
    
    @validator('query')
    def validate_query(cls, v):
        if not v or not v.strip():
            raise ValueError("搜索关键词不能为空")
        return v.strip()

class SearchResult(BaseModel):
    """单个搜索结果"""
    id: int
    title: str
    content: str
    category: str
    relevance_score: float = Field(..., description="相关度分数")
    snippet: Optional[str] = Field(None, description="高亮摘要")

class SearchResponse(BaseModel):
    """搜索响应模型"""
    query: str = Field(..., description="搜索关键词")
    total: int = Field(..., description="总结果数")
    results: List[SearchResult] = Field(..., description="搜索结果列表")
    took: float = Field(..., description="查询耗时(毫秒)")

class BulkInsertRequest(BaseModel):
    """批量插入请求"""
    documents: List[Document] = Field(..., description="文档列表")
    
    @validator('documents')
    def validate_documents(cls, v):
        if not v:
            raise ValueError("文档列表不能为空")
        if len(v) > 1000:
            raise ValueError("单次批量插入不能超过1000条记录")
        return v

class UpdateRequest(BaseModel):
    """更新请求模型"""
    id: int = Field(..., description="文档ID")
    title: Optional[str] = Field(None, description="新标题")
    content: Optional[str] = Field(None, description="新内容")
    category: Optional[str] = Field(None, description="新分类")

class ApiResponse(BaseModel):
    """通用API响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    error_code: Optional[str] = Field(None, description="错误代码")

class HealthCheck(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="版本信息")
    manticore_version: Optional[str] = Field(None, description="Manticore版本")
    uptime: float = Field(..., description="运行时间(秒)")
    total_documents: int = Field(..., description="文档总数")
